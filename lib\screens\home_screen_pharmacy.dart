import 'package:flutter/material.dart';

import 'dart:math';
import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import 'pharmacy_inventory_screen.dart';
import 'pharmacy_reports_screen.dart';
import '../services/order_service.dart';
import '../services/pharmacy_service.dart';
import 'pharmacy_settings_screen.dart';
import 'accepted_orders_screen.dart';

class PharmacyOrdersScreen extends StatefulWidget {
  final bool isDark;
  final VoidCallback onToggleDarkMode;
  final VoidCallback onLogout;

  const PharmacyOrdersScreen({
    Key? key,
    required this.isDark,
    required this.onToggleDarkMode,
    required this.onLogout,
  }) : super(key: key);

  @override
  _PharmacyOrdersScreenState createState() => _PharmacyOrdersScreenState();
}

class _PharmacyOrdersScreenState extends State<PharmacyOrdersScreen>
    with TickerProviderStateMixin {
  late AnimationController _waveController;
  late AnimationController _cardAnimationController;
  late AnimationController _headerAnimationController;
  late AnimationController _fabAnimationController;

  late Animation<double> _waveAnimation;
  late Animation<double> _cardSlideAnimation;
  late Animation<double> _cardFadeAnimation;
  late Animation<double> _headerSlideAnimation;
  late Animation<double> _fabScaleAnimation;

  final _scrollController = ScrollController();
  double _scrollOffset = 0;
  bool _isDarkMode = false;
  int _currentIndex = 0;

  // متغيرات الطلبات الحقيقية
  List<Map<String, dynamic>> _orders = [];
  bool _isLoading = false;
  String? _errorMessage;
  String _pharmacyName = 'الصيدلية';

  // الطلبات الوهمية للعرض (سيتم استبدالها)
  final List<Map<String, dynamic>> _dummyOrders = [
    {
      'id': 'ORD-2023-001',
      'customer': 'أحمد محمد',
      'medicines': ['باراسيتامول 500mg', 'فيتامين C 1000mg'],
      'time': 'منذ 5 دقائق',
      'status': 'new',
      'prescription': 'assets/images/prescription1.png',
      'offer': null,
      'avatarColor': Colors.blue,
    },
    {
      'id': 'ORD-2023-002',
      'customer': 'سارة خالد',
      'medicines': ['أموكسيسيلين 500mg', 'مضاد حيوي'],
      'time': 'منذ 15 دقيقة',
      'status': 'pending',
      'prescription': null,
      'offer': 75.50,
      'avatarColor': Colors.pink,
    },
    {
      'id': 'ORD-2023-003',
      'customer': 'علي حسن',
      'medicines': ['فيتامين D3', 'كالسيوم', 'ماغنيسيوم'],
      'time': 'منذ 25 دقيقة',
      'status': 'accepted',
      'prescription': 'assets/images/prescription2.png',
      'offer': 120.0,
      'avatarColor': Colors.green,
    },
    {
      'id': 'ORD-2023-004',
      'customer': 'ليلى عبدالله',
      'medicines': ['أدوية ضغط الدم', 'مدر للبول'],
      'time': 'منذ ساعة',
      'status': 'ready',
      'prescription': 'assets/images/prescription3.png',
      'offer': 65.75,
      'avatarColor': Colors.purple,
    },
  ];

  /// تحميل بيانات الصيدلية
  Future<void> _loadPharmacyData() async {
    try {
      final pharmacyName = await PharmacyService.getPharmacyName();
      if (mounted) {
        setState(() {
          _pharmacyName = pharmacyName;
        });
      }
    } catch (e) {
      print('خطأ في تحميل بيانات الصيدلية: $e');
    }
  }

  /// تحميل الطلبات من الخادم
  Future<void> _loadOrders() async {
    if (mounted) {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });
    }

    try {
      // التحقق من تسجيل الدخول أولاً
      final token = await PharmacyService.getAccessToken();
      if (token == null) {
        if (mounted) {
          setState(() {
            _errorMessage = 'يجب تسجيل الدخول أولاً';
            _isLoading = false;
            _orders = List<Map<String, dynamic>>.from(_dummyOrders);
          });
        }
        return;
      }

      final result = await OrderService.getPharmacyOrders(
        page: 1,
        limit: 20,
      );

      print('🔍 نتيجة جلب الطلبات: ${result.toString()}');

      if (mounted) {
        if (result['success'] == true) {
          final orders = result['data'] ?? [];
          print('📋 عدد الطلبات المستلمة: ${orders.length}');

          setState(() {
            _orders = List<Map<String, dynamic>>.from(orders);
            _isLoading = false;
          });

          // إذا لم توجد طلبات، استخدم الوهمية للعرض
          if (orders.isEmpty) {
            setState(() {
              _orders = List<Map<String, dynamic>>.from(_dummyOrders);
            });
          }
        } else {
          print('❌ خطأ في الاستجابة: ${result['message']}');
          setState(() {
            _errorMessage = result['message'] ?? 'فشل في تحميل الطلبات';
            _isLoading = false;
            // استخدام الطلبات الوهمية في حالة الفشل
            _orders = List<Map<String, dynamic>>.from(_dummyOrders);
          });
        }
      }
    } catch (e) {
      print('❌ خطأ في تحميل الطلبات: $e');
      if (mounted) {
        setState(() {
          _errorMessage = 'خطأ في الاتصال: ${e.toString()}';
          _isLoading = false;
          // استخدام الطلبات الوهمية في حالة الخطأ
          _orders = List<Map<String, dynamic>>.from(_dummyOrders);
        });
      }
    }
  }

  @override
  void initState() {
    super.initState();
    _isDarkMode = widget.isDark;
    _loadPharmacyData();
    _loadOrders();

    // تحديث الطلبات كل 30 ثانية
    Timer.periodic(const Duration(seconds: 30), (timer) {
      if (mounted) {
        _loadOrders();
      } else {
        timer.cancel();
      }
    });
    _loadDarkMode();

    // تهيئة جميع الأنيميشن
    _initializeAnimations();

    // متابعة حركة التمرير
    _scrollController.addListener(() {
      setState(() {
        _scrollOffset = _scrollController.offset;
      });
    });
  }

  void _initializeAnimations() {
    // تحريك الموجة الخلفية
    _waveController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 8),
    )..repeat(reverse: true);

    _waveAnimation = Tween<double>(begin: -20, end: 20).animate(
      CurvedAnimation(parent: _waveController, curve: Curves.easeInOut),
    );

    // أنيميشن الكروت
    _cardAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _cardSlideAnimation = Tween<double>(begin: 50, end: 0).animate(
      CurvedAnimation(parent: _cardAnimationController, curve: Curves.elasticOut),
    );

    _cardFadeAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _cardAnimationController, curve: Curves.easeInOut),
    );

    // أنيميشن الهيدر
    _headerAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );

    _headerSlideAnimation = Tween<double>(begin: -100, end: 0).animate(
      CurvedAnimation(parent: _headerAnimationController, curve: Curves.bounceOut),
    );

    // أنيميشن الـ FAB
    _fabAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400),
    );

    _fabScaleAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _fabAnimationController, curve: Curves.elasticOut),
    );

    // بدء الأنيميشن
    _headerAnimationController.forward();
    _cardAnimationController.forward();

    // تأخير الـ FAB قليلاً
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) _fabAnimationController.forward();
    });
  }

  Future<void> _loadDarkMode() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _isDarkMode = prefs.getBool('pharmacy_dark_mode') ?? widget.isDark;
    });
  }

  Future<void> _toggleDarkMode() async {
    setState(() {
      _isDarkMode = !_isDarkMode;
    });
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('pharmacy_dark_mode', _isDarkMode);

    // استدعاء الدالة الأصلية إذا كانت موجودة
    widget.onToggleDarkMode();
  }

  @override
  void dispose() {
    _waveController.dispose();
    _cardAnimationController.dispose();
    _headerAnimationController.dispose();
    _fabAnimationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Widget _getCurrentPage() {
    switch (_currentIndex) {
      case 0:
        return _buildOrdersPage();
      case 1:
        return AcceptedOrdersScreen(
          isDark: _isDarkMode,
          onToggleDarkMode: _toggleDarkMode,
        );
      case 2:
        return PharmacyInventoryScreen(
          isDark: _isDarkMode,
          onToggleDarkMode: _toggleDarkMode,
        );
      case 3:
        return PharmacyReportsScreen(
          isDark: _isDarkMode,
          onToggleDarkMode: _toggleDarkMode,
        );
      case 4:
        return PharmacySettingsScreen(
          isDark: _isDarkMode,
          onToggleDarkMode: _toggleDarkMode,
          onLogout: widget.onLogout,
        );
      default:
        return _buildOrdersPage();
    }
  }

  @override
  Widget build(BuildContext context) {
    final bgColor = _isDarkMode ? const Color(0xFF0F0F23) : const Color(0xFFF8FAFC);
    final textColor = _isDarkMode ? Colors.white : const Color(0xFF1E293B);
    const primaryColor = Color(0xFF00BF63);


    return Scaffold(
      backgroundColor: bgColor,
      body: _getCurrentPage(),
      bottomNavigationBar: _buildBottomNavBar(primaryColor, textColor),
      floatingActionButton: _currentIndex == 0 ? AnimatedBuilder(
        animation: _fabAnimationController,
        builder: (context, child) {
          return Transform.scale(
            scale: _fabScaleAnimation.value,
            child: Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    primaryColor,
                    primaryColor.withValues(alpha: 0.8),
                  ],
                ),
                boxShadow: [
                  BoxShadow(
                    color: primaryColor.withValues(alpha: 0.4),
                    blurRadius: 20,
                    offset: const Offset(0, 8),
                    spreadRadius: 0,
                  ),
                  BoxShadow(
                    color: primaryColor.withValues(alpha: 0.2),
                    blurRadius: 40,
                    offset: const Offset(0, 16),
                    spreadRadius: -8,
                  ),
                ],
              ),
              child: FloatingActionButton(
                backgroundColor: Colors.transparent,
                elevation: 0,
                onPressed: () {
                  _loadOrders();
                },
                child: _isLoading
                    ? SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Icon(
                        Icons.refresh,
                        color: Colors.white,
                        size: 28,
                      ),
              ),
            ),
          );
        },
      ) : null,
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
    );
  }

  Widget _buildOrdersPage() {
    final bgColor = _isDarkMode ? const Color(0xFF1B1B1B) : Colors.white;
    final textColor = _isDarkMode ? Colors.white : const Color(0xFF1B1B1B);
    const primaryColor = Color(0xFF00BF63);
    final cardColor = _isDarkMode
        ? const Color(0xFF2A2A2A)
        : const Color(0xFFF8F9FA);
    final accentColor = _isDarkMode
        ? const Color(0xFF3A3A3A)
        : const Color(0xFFE8F5E8);

    return Stack(
        children: [
          // خلفية متحركة
          Positioned.fill(
            child: AnimatedBuilder(
              animation: _waveAnimation,
              builder: (context, child) {
                return Transform.translate(
                  offset: Offset(0, _waveAnimation.value),
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          primaryColor.withValues(alpha: 0.03),
                          primaryColor.withValues(alpha: 0.01),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ),

          // محتوى الشاشة
          CustomScrollView(
            controller: _scrollController,
            physics: const BouncingScrollPhysics(),
            slivers: [
              SliverAppBar(
                expandedHeight: 320,
                floating: false,
                pinned: true,
                backgroundColor: bgColor,
                elevation: 0,
                leading: Container(),
                actions: [
                  AnimatedBuilder(
                    animation: _fabAnimationController,
                    builder: (context, child) {
                      return Transform.scale(
                        scale: _fabScaleAnimation.value,
                        child: Container(
                          margin: const EdgeInsets.only(right: 16),
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: LinearGradient(
                              colors: [primaryColor, primaryColor.withValues(alpha: 0.8)],
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: primaryColor.withValues(alpha: 0.3),
                                blurRadius: 12,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: IconButton(
                            icon: _isLoading
                                ? SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                    ),
                                  )
                                : const Icon(Icons.refresh, color: Colors.white),
                            onPressed: _isLoading ? null : _loadOrders,
                            tooltip: 'تحديث الطلبات',
                          ),
                        ),
                      );
                    },
                  ),
                ],
                flexibleSpace: FlexibleSpaceBar(
                  title: Transform.translate(
                    offset: Offset(0, _scrollOffset > 150 ? 0 : 20),
                    child: Opacity(
                      opacity: _scrollOffset > 150 ? 1.0 : 0.0,
                      child: Text(
                        _pharmacyName,
                        style: TextStyle(
                          color: textColor,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          fontFamily: 'Tajawal',
                        ),
                      ),
                    ),
                  ),
                  background: AnimatedBuilder(
                    animation: _headerAnimationController,
                    builder: (context, child) {
                      return Transform.translate(
                        offset: Offset(_headerSlideAnimation.value, 0),
                        child: Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: _isDarkMode
                                  ? [
                                      const Color(0xFF1E1E3F).withValues(alpha: 0.8),
                                      const Color(0xFF2D2D5F).withValues(alpha: 0.6),
                                      Colors.transparent,
                                    ]
                                  : [
                                      primaryColor.withValues(alpha: 0.1),
                                      primaryColor.withValues(alpha: 0.05),
                                      Colors.transparent,
                                    ],
                            ),
                          ),
                          child: SafeArea(
                            child: Padding(
                              padding: const EdgeInsets.all(24),
                              child: Column(
                                children: [
                                  // Enhanced Header
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      // Enhanced Profile section
                                      Row(
                                        children: [
                                          Container(
                                            width: 60,
                                            height: 60,
                                            decoration: BoxDecoration(
                                              shape: BoxShape.circle,
                                              gradient: LinearGradient(
                                                begin: Alignment.topLeft,
                                                end: Alignment.bottomRight,
                                                colors: [
                                                  primaryColor,
                                                  primaryColor.withValues(alpha: 0.7),
                                                ],
                                              ),
                                              boxShadow: [
                                                BoxShadow(
                                                  color: primaryColor.withValues(alpha: 0.4),
                                                  blurRadius: 16,
                                                  offset: const Offset(0, 6),
                                                ),
                                              ],
                                            ),
                                            child: const Icon(
                                              Icons.local_pharmacy,
                                              color: Colors.white,
                                              size: 28,
                                            ),
                                          ),
                                          const SizedBox(width: 16),
                                          Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                        Text(
                                          'مرحباً بك',
                                          style: TextStyle(
                                            color: textColor.withValues(alpha: 0.7),
                                            fontSize: 14,
                                            fontFamily: 'Tajawal',
                                          ),
                                        ),
                                        Text(
                                          'صيدلية النور',
                                          style: TextStyle(
                                            color: textColor,
                                            fontSize: 18,
                                            fontWeight: FontWeight.bold,
                                            fontFamily: 'Tajawal',
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                                // Dark mode toggle
                                Container(
                                  decoration: BoxDecoration(
                                    color: accentColor,
                                    borderRadius: BorderRadius.circular(12),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withValues(alpha: 0.1),
                                        blurRadius: 8,
                                        offset: const Offset(0, 2),
                                      ),
                                    ],
                                  ),
                                  child: IconButton(
                                    icon: AnimatedSwitcher(
                                      duration: const Duration(milliseconds: 300),
                                      child: Icon(
                                        _isDarkMode ? Icons.light_mode : Icons.dark_mode,
                                        key: ValueKey(_isDarkMode),
                                        color: primaryColor,
                                        size: 24,
                                      ),
                                    ),
                                    onPressed: () async {
                                      await _toggleDarkMode();
                                      // إظهار رسالة تأكيد
                                      if (mounted) {
                                        ScaffoldMessenger.of(context).showSnackBar(
                                          SnackBar(
                                            content: Text(
                                              _isDarkMode
                                                  ? 'تم التبديل إلى الوضع المظلم'
                                                  : 'تم التبديل إلى الوضع الفاتح',
                                              style: const TextStyle(
                                                fontFamily: 'Tajawal',
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                            backgroundColor: primaryColor,
                                            duration: const Duration(milliseconds: 1500),
                                            behavior: SnackBarBehavior.floating,
                                            shape: RoundedRectangleBorder(
                                              borderRadius: BorderRadius.circular(10),
                                            ),
                                          ),
                                        );
                                      }
                                    },
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 30),
                            // Welcome message with animation
                            AnimatedBuilder(
                              animation: _waveController,
                              builder: (context, child) {
                                return Transform.translate(
                                  offset: Offset(0, _waveController.value * 2),
                                  child: Column(
                                    children: [
                                      Container(
                                        padding: const EdgeInsets.all(16),
                                        decoration: BoxDecoration(
                                          color: primaryColor.withValues(alpha: 0.1),
                                          shape: BoxShape.circle,
                                        ),
                                        child: Icon(
                                          Icons.medical_services,
                                          size: 40,
                                          color: primaryColor,
                                        ),
                                      ),
                                      const SizedBox(height: 16),
                                      Text(
                                        'إدارة طلبات الأدوية',
                                        style: TextStyle(
                                          color: textColor,
                                          fontSize: 24,
                                          fontWeight: FontWeight.bold,
                                          fontFamily: 'Tajawal',
                                        ),
                                      ),
                                      const SizedBox(height: 8),
                                      Text(
                                        'تابع وأدر جميع طلبات العملاء بسهولة',
                                        style: TextStyle(
                                          color: textColor.withValues(alpha: 0.6),
                                          fontSize: 16,
                                          fontFamily: 'Tajawal',
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ],
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),

              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'إحصائيات اليوم',
                        style: TextStyle(
                          color: textColor,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          fontFamily: 'Tajawal',
                        ),
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          _buildStatCard(
                            'طلبات جديدة',
                            '12',
                            Icons.new_releases,
                            const Color(0xFF2196F3),
                          ),
                          const SizedBox(width: 12),
                          _buildStatCard(
                            'قيد التجهيز',
                            '8',
                            Icons.timer,
                            const Color(0xFFFF9800),
                          ),
                          const SizedBox(width: 12),
                          _buildStatCard(
                            'جاهزة للتسليم',
                            '5',
                            Icons.delivery_dining,
                            primaryColor,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

              // عرض مؤشر التحميل أو الأخطاء أو الطلبات
              if (_isLoading)
                const SliverFillRemaining(
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF00BF63)),
                        ),
                        SizedBox(height: 16),
                        Text(
                          'جاري تحميل الطلبات...',
                          style: TextStyle(
                            fontFamily: 'Tajawal',
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              if (_errorMessage != null)
                SliverFillRemaining(
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 64,
                          color: Colors.red,
                        ),
                        SizedBox(height: 16),
                        Text(
                          _errorMessage!,
                          style: TextStyle(
                            fontFamily: 'Tajawal',
                            fontSize: 16,
                            color: Colors.red,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: _loadOrders,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Color(0xFF00BF63),
                            foregroundColor: Colors.white,
                          ),
                          child: Text(
                            'إعادة المحاولة',
                            style: TextStyle(fontFamily: 'Tajawal'),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              if (_orders.isEmpty)
                const SliverFillRemaining(
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.inbox_outlined,
                          size: 64,
                          color: Colors.grey,
                        ),
                        SizedBox(height: 16),
                        Text(
                          'لا توجد طلبات حالياً',
                          style: TextStyle(
                            fontFamily: 'Tajawal',
                            fontSize: 18,
                            color: Colors.grey,
                          ),
                        ),
                        SizedBox(height: 8),
                        Text(
                          'ستظهر الطلبات الجديدة هنا',
                          style: TextStyle(
                            fontFamily: 'Tajawal',
                            fontSize: 14,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              if (_orders.isNotEmpty)
                SliverList(
                  delegate: SliverChildBuilderDelegate((context, index) {
                    final order = _orders[index];
                    return Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                      child: _buildOrderCard(
                        order,
                        index,
                        textColor,
                        cardColor,
                        primaryColor,
                      ),
                    );
                  }, childCount: _orders.length),
                ),
            ],
          ),
        ],
      );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
    Color textColor,
    Color bgColor,
    Color accentColor,
  ) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: bgColor,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: color.withValues(alpha: 0.2),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: color.withValues(alpha: 0.1),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(height: 12),
            Text(
              value,
              style: TextStyle(
                color: textColor,
                fontWeight: FontWeight.bold,
                fontSize: 24,
                fontFamily: 'Tajawal',
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: TextStyle(
                color: textColor.withValues(alpha: 0.7),
                fontSize: 12,
                fontFamily: 'Tajawal',
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderCard(
    Map<String, dynamic> order,
    int index,
    Color textColor,
    Color cardColor,
    Color primaryColor,
  ) {
    final statusInfo = _getStatusInfo(order['status'] ?? 'pending');
    final avatarColor = order['avatarColor'] ?? Colors.blue;
    final shadowColor = _isDarkMode
        ? Colors.black.withValues(alpha: 0.3)
        : Colors.grey.withValues(alpha: 0.1);
    final accentColor = _isDarkMode
        ? const Color(0xFF2D2D5F)
        : const Color(0xFFE8F5E8);

    return AnimatedBuilder(
      animation: Listenable.merge([_waveController, _cardAnimationController]),
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(
            _cardSlideAnimation.value,
            sin(_waveController.value * 2 * pi + index * 0.5) * 3,
          ),
          child: Opacity(
            opacity: _cardFadeAnimation.value,
            child: child,
          ),
        );
      },
      child: Container(
        margin: EdgeInsets.fromLTRB(20, 12, 20, 12),
        child: Material(
          borderRadius: BorderRadius.circular(24),
          color: cardColor,
          elevation: 0,
          shadowColor: shadowColor,
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(24),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: _isDarkMode
                    ? [
                        const Color(0xFF1E1E3F),
                        const Color(0xFF2D2D5F),
                      ]
                    : [
                        Colors.white,
                        const Color(0xFFF8FAFC),
                      ],
              ),
              boxShadow: [
                BoxShadow(
                  color: shadowColor,
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                  spreadRadius: 0,
                ),
                BoxShadow(
                  color: primaryColor.withValues(alpha: 0.1),
                  blurRadius: 30,
                  offset: const Offset(0, 4),
                  spreadRadius: -5,
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(24),
              child: InkWell(
                borderRadius: BorderRadius.circular(24),
                onTap: () => _showOrderDetails(order),
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header Row with Avatar and Status
                      Row(
                        children: [
                          // Enhanced Avatar with Gradient
                          Container(
                            width: 56,
                            height: 56,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  avatarColor.withValues(alpha: 0.8),
                                  avatarColor.withValues(alpha: 0.6),
                                ],
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: avatarColor.withValues(alpha: 0.3),
                                  blurRadius: 12,
                                  offset: const Offset(0, 4),
                                ),
                              ],
                            ),
                            child: Center(
                              child: Text(
                                (order['contactInfo']?['name'] ?? 'م').toString().substring(0, 1),
                                style: TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 20,
                                  fontFamily: 'Tajawal',
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),

                          // Customer Info
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  order['contactInfo']?['name'] ?? 'مستخدم غير محدد',
                                  style: TextStyle(
                                    color: textColor,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 18,
                                    fontFamily: 'Tajawal',
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Row(
                                  children: [
                                    Icon(
                                      Icons.access_time,
                                      size: 14,
                                      color: textColor.withValues(alpha: 0.6),
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      order['time'] ?? 'منذ قليل',
                                      style: TextStyle(
                                        color: textColor.withValues(alpha: 0.6),
                                        fontSize: 12,
                                        fontFamily: 'Tajawal',
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),

                          // Enhanced Status Badge
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 8,
                            ),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  statusInfo.color.withValues(alpha: 0.2),
                                  statusInfo.color.withValues(alpha: 0.1),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(20),
                              border: Border.all(
                                color: statusInfo.color.withValues(alpha: 0.3),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  statusInfo.icon,
                                  size: 16,
                                  color: statusInfo.color,
                                ),
                                const SizedBox(width: 6),
                                Text(
                                  statusInfo.text,
                                  style: TextStyle(
                                    color: statusInfo.color,
                                    fontFamily: 'Tajawal',
                                    fontSize: 12,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                    ],
                  ),

                  const SizedBox(height: 20),

                  // Medicine Information Card
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: accentColor.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: primaryColor.withValues(alpha: 0.2),
                        width: 1,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.medical_services,
                              color: primaryColor,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'تفاصيل الدواء',
                              style: TextStyle(
                                color: textColor,
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                                fontFamily: 'Tajawal',
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        Text(
                          order['medicineRequest']?['name'] ?? 'دواء غير محدد',
                          style: TextStyle(
                            color: textColor,
                            fontWeight: FontWeight.w600,
                            fontSize: 15,
                            fontFamily: 'Tajawal',
                          ),
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: primaryColor.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                'الكمية: ${order['medicineRequest']?['quantity'] ?? 0}',
                                style: TextStyle(
                                  color: primaryColor,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                  fontFamily: 'Tajawal',
                                ),
                              ),
                            ),
                          ],
                        ),
                        if (order['medicineRequest']?['description'] != null) ...[
                          const SizedBox(height: 8),
                          Text(
                            order['medicineRequest']['description'],
                            style: TextStyle(
                              color: textColor.withValues(alpha: 0.7),
                              fontSize: 13,
                              fontFamily: 'Tajawal',
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ],
                    ),
                  ),
                  if ((order['medicineRequest']?['prescriptionImageUrl'] != null &&
                       order['medicineRequest']['prescriptionImageUrl'].toString().isNotEmpty) ||
                      (order['prescription']?['imageUrl'] != null &&
                       order['prescription']['imageUrl'].toString().isNotEmpty)) ...[
                    const SizedBox(height: 12),
                    GestureDetector(
                      onTap: () => _showPrescriptionImage(
                        order['medicineRequest']?['prescriptionImageUrl'] ??
                        order['prescription']?['imageUrl'] ??
                        ''
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Container(
                          height: 80,
                          width: double.infinity,
                          color: Colors.grey[300],
                          child: const Center(
                            child: Icon(Icons.image, color: Colors.grey, size: 32),
                          ),
                        ),
                      ),
                    ),
                  ],

                  const SizedBox(height: 20),

                  // Action Buttons
                  if (order['status'] == 'pending') ...[
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            height: 48,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [primaryColor, primaryColor.withValues(alpha: 0.8)],
                              ),
                              borderRadius: BorderRadius.circular(16),
                              boxShadow: [
                                BoxShadow(
                                  color: primaryColor.withValues(alpha: 0.3),
                                  blurRadius: 12,
                                  offset: const Offset(0, 4),
                                ),
                              ],
                            ),
                            child: ElevatedButton(
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.transparent,
                                shadowColor: Colors.transparent,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(16),
                                ),
                              ),
                              onPressed: () => _showOfferDialog(order),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const Icon(Icons.local_offer, size: 18),
                                  const SizedBox(width: 8),
                                  const Text(
                                    'تقديم عرض',
                                    style: TextStyle(
                                      fontFamily: 'Tajawal',
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Container(
                            height: 48,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [Colors.orange, Colors.orange.withValues(alpha: 0.8)],
                              ),
                              borderRadius: BorderRadius.circular(16),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.orange.withValues(alpha: 0.3),
                                  blurRadius: 12,
                                  offset: const Offset(0, 4),
                                ),
                              ],
                            ),
                            child: ElevatedButton(
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.transparent,
                                shadowColor: Colors.transparent,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(16),
                                ),
                              ),
                              onPressed: () => _acceptOrder(order),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const Icon(Icons.check_circle, size: 18),
                                  const SizedBox(width: 8),
                                  const Text(
                                    'قبول الطلب',
                                    style: TextStyle(
                                      fontFamily: 'Tajawal',
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ] else if (order['offer'] != null) ...[
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            primaryColor.withValues(alpha: 0.1),
                            primaryColor.withValues(alpha: 0.05),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: primaryColor.withValues(alpha: 0.3),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.monetization_on,
                            color: primaryColor,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'العرض المقدم: ${order['offer']} ر.س',
                            style: TextStyle(
                              color: primaryColor,
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                              fontFamily: 'Tajawal',
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _showOrderDetails(Map<String, dynamic> order) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          decoration: BoxDecoration(
            color: _isDarkMode ? const Color(0xFF1E293B) : Colors.white,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
          ),
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'تفاصيل الطلب',
                style: TextStyle(
                  color: _isDarkMode ? Colors.white : const Color(0xFF0F172A),
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Tajawal',
                ),
              ),
              const SizedBox(height: 24),
              _buildDetailRow('رقم الطلب', order['id']),
              _buildDetailRow('العميل', order['customer']),
              _buildDetailRow('الوقت', order['time']),
              const SizedBox(height: 16),
              const Divider(),
              const SizedBox(height: 16),
              Text(
                'الأدوية المطلوبة',
                style: TextStyle(
                  color: _isDarkMode ? Colors.white : const Color(0xFF0F172A),
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  fontFamily: 'Tajawal',
                ),
              ),
              const SizedBox(height: 12),
              ...order['medicines']
                  .map<Widget>(
                    (medicine) => Padding(
                      padding: const EdgeInsets.symmetric(vertical: 4),
                      child: Row(
                        children: [
                          Icon(
                            Icons.medical_services,
                            size: 16,
                            color: const Color(0xFF00C853),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              medicine,
                              style: TextStyle(
                                color: _isDarkMode
                                    ? Colors.white
                                    : const Color(0xFF0F172A),
                                fontFamily: 'Tajawal',
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                  )
                  .toList(),
              const SizedBox(height: 24),
              if (order['prescription'] != null)
                Column(
                  children: [
                    const Text(
                      'صورة الوصفة الطبية',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontFamily: 'Tajawal',
                      ),
                    ),
                    const SizedBox(height: 12),
                    GestureDetector(
                      onTap: () =>
                          _showPrescriptionImage(order['prescription']?['imageUrl'] ?? ''),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: _buildPrescriptionImage(
                          order['prescription']?['imageUrl'] ?? '',
                          height: 150,
                        ),
                      ),
                    ),
                  ],
                ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () => Navigator.pop(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF00C853),
                  foregroundColor: Colors.white,
                  minimumSize: const Size(double.infinity, 50),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  'إغلاق',
                  style: TextStyle(fontFamily: 'Tajawal'),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Text(
            '$label: ',
            style: TextStyle(
              color: _isDarkMode ? Colors.white70 : Colors.grey[700],
              fontFamily: 'Tajawal',
            ),
          ),
          Text(
            value,
            style: TextStyle(
              color: _isDarkMode ? Colors.white : Colors.black,
              fontWeight: FontWeight.bold,
              fontFamily: 'Tajawal',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPrescriptionImage(String imagePath, {double? height}) {
    if (imagePath.isEmpty) {
      return Container(
        height: height ?? 100,
        width: double.infinity,
        color: Colors.grey[300],
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.image_not_supported, color: Colors.grey, size: 32),
              SizedBox(height: 4),
              Text(
                'لا توجد صورة',
                style: TextStyle(
                  color: Colors.grey,
                  fontSize: 12,
                  fontFamily: 'Tajawal',
                ),
              ),
            ],
          ),
        ),
      );
    }

    if (imagePath.startsWith('http')) {
      return Image.network(
        imagePath,
        height: height ?? 100,
        width: double.infinity,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            height: height ?? 100,
            width: double.infinity,
            color: Colors.grey[300],
            child: const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.broken_image, color: Colors.red, size: 32),
                  SizedBox(height: 4),
                  Text(
                    'خطأ في تحميل الصورة',
                    style: TextStyle(
                      color: Colors.red,
                      fontSize: 12,
                      fontFamily: 'Tajawal',
                    ),
                  ),
                ],
              ),
            ),
          );
        },
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return Container(
            height: height ?? 100,
            width: double.infinity,
            color: Colors.grey[300],
            child: const Center(
              child: CircularProgressIndicator(),
            ),
          );
        },
      );
    } else {
      return Image.asset(
        imagePath,
        height: height ?? 100,
        width: double.infinity,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            height: height ?? 100,
            width: double.infinity,
            color: Colors.grey[300],
            child: const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.broken_image, color: Colors.red, size: 32),
                  SizedBox(height: 4),
                  Text(
                    'صورة غير موجودة',
                    style: TextStyle(
                      color: Colors.red,
                      fontSize: 12,
                      fontFamily: 'Tajawal',
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      );
    }
  }

  void _showPrescriptionImage(String imagePath) {
    if (imagePath.isEmpty) return;

    showDialog(
      context: context,
      builder: (context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          insetPadding: const EdgeInsets.all(16),
          child: InteractiveViewer(
            panEnabled: true,
            minScale: 0.5,
            maxScale: 3.0,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: imagePath.startsWith('http')
                  ? Image.network(
                      imagePath,
                      fit: BoxFit.contain,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          padding: const EdgeInsets.all(20),
                          child: const Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(Icons.broken_image, color: Colors.white, size: 64),
                              SizedBox(height: 16),
                              Text(
                                'خطأ في تحميل الصورة',
                                style: TextStyle(color: Colors.white, fontSize: 16),
                              ),
                            ],
                          ),
                        );
                      },
                    )
                  : Image.asset(imagePath, fit: BoxFit.contain),
            ),
          ),
        );
      },
    );
  }

  void _showOfferDialog(Map<String, dynamic> order) {
    final priceController = TextEditingController();
    final quantityController = TextEditingController(
      text: (order['medicineRequest']?['quantity'] ?? 1).toString(),
    );
    final notesController = TextEditingController();
    final timeController = TextEditingController(text: '30');

    showDialog(
      context: context,
      builder: (context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'تقديم عرض سعر',
                  style: TextStyle(
                    color: _isDarkMode ? Colors.white : Colors.black,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    fontFamily: 'Tajawal',
                  ),
                ),
                const SizedBox(height: 24),
                // حقل السعر
                TextField(
                  controller: priceController,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    labelText: 'السعر (ج.م)',
                    prefixIcon: const Icon(Icons.attach_money),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    filled: true,
                    fillColor: _isDarkMode
                        ? const Color(0xFF1E293B)
                        : Colors.grey[100],
                  ),
                  style: TextStyle(
                    color: _isDarkMode ? Colors.white : Colors.black,
                    fontFamily: 'Tajawal',
                  ),
                ),
                const SizedBox(height: 16),
                // حقل الكمية المتاحة
                TextField(
                  controller: quantityController,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    labelText: 'الكمية المتاحة',
                    prefixIcon: const Icon(Icons.inventory),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    filled: true,
                    fillColor: _isDarkMode
                        ? const Color(0xFF1E293B)
                        : Colors.grey[100],
                  ),
                  style: TextStyle(
                    color: _isDarkMode ? Colors.white : Colors.black,
                    fontFamily: 'Tajawal',
                  ),
                ),
                const SizedBox(height: 16),
                // حقل الوقت المقدر
                TextField(
                  controller: timeController,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    labelText: 'الوقت المقدر (دقيقة)',
                    prefixIcon: const Icon(Icons.access_time),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    filled: true,
                    fillColor: _isDarkMode
                        ? const Color(0xFF1E293B)
                        : Colors.grey[100],
                  ),
                  style: TextStyle(
                    color: _isDarkMode ? Colors.white : Colors.black,
                    fontFamily: 'Tajawal',
                  ),
                ),
                const SizedBox(height: 16),
                // حقل الملاحظات
                TextField(
                  controller: notesController,
                  maxLines: 2,
                  decoration: InputDecoration(
                    labelText: 'ملاحظات (اختياري)',
                    prefixIcon: const Icon(Icons.note),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    filled: true,
                    fillColor: _isDarkMode
                        ? const Color(0xFF1E293B)
                        : Colors.grey[100],
                  ),
                  style: TextStyle(
                    color: _isDarkMode ? Colors.white : Colors.black,
                    fontFamily: 'Tajawal',
                  ),
                ),
                const SizedBox(height: 24),
                Row(
                  children: [
                    Expanded(
                      child: TextButton(
                        onPressed: () => Navigator.pop(context),
                        style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: const Text(
                          'إلغاء',
                          style: TextStyle(fontFamily: 'Tajawal'),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          if (priceController.text.isNotEmpty &&
                              quantityController.text.isNotEmpty &&
                              timeController.text.isNotEmpty) {
                            Navigator.pop(context);
                            _submitOffer(
                              order,
                              double.parse(priceController.text),
                              int.parse(quantityController.text),
                              notesController.text.trim().isEmpty
                                  ? null
                                  : notesController.text.trim(),
                              int.parse(timeController.text),
                            );
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF00C853),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: const Text(
                          'إرسال العرض',
                          style: TextStyle(fontFamily: 'Tajawal'),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Future<void> _submitOffer(
    Map<String, dynamic> order,
    double price,
    int availableQuantity,
    String? notes,
    int estimatedTime,
  ) async {
    try {
      // إظهار مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          backgroundColor: _isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(color: Color(0xFF00BF63)),
              const SizedBox(height: 20),
              Text(
                'جاري إرسال العرض...',
                style: TextStyle(
                  color: _isDarkMode ? Colors.white : Colors.black,
                  fontFamily: 'Tajawal',
                ),
              ),
            ],
          ),
        ),
      );

      // إرسال العرض للخادم
      final result = await OrderService.submitOffer(
        orderId: order['id'] ?? order['_id'],
        price: price,
        availableQuantity: availableQuantity,
        notes: notes ?? 'عرض من صيدلية $_pharmacyName',
        estimatedTime: estimatedTime,
      );

      if (mounted) {
        Navigator.pop(context); // إغلاق مؤشر التحميل

        if (result['success'] == true) {
          // تحديث البيانات محلياً
          setState(() {
            order['offer'] = price;
            order['status'] = 'offer_submitted';
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                result['message'] ?? 'تم إرسال العرض بنجاح',
                style: const TextStyle(fontFamily: 'Tajawal'),
              ),
              backgroundColor: const Color(0xFF00C853),
              behavior: SnackBarBehavior.floating,
            ),
          );

          // إعادة تحميل الطلبات لتحديث الحالة
          _loadOrders();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                result['message'] ?? 'فشل في إرسال العرض',
                style: const TextStyle(fontFamily: 'Tajawal'),
              ),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        Navigator.pop(context); // إغلاق مؤشر التحميل
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'خطأ في الاتصال: ${e.toString()}',
              style: const TextStyle(fontFamily: 'Tajawal'),
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  void _acceptOrder(Map<String, dynamic> order) {
    setState(() {
      order['status'] = 'accepted';
    });

    // انتقال تلقائي إلى صفحة الطلبات المقبولة
    setState(() {
      _currentIndex = 1; // الطلبات المقبولة
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text(
          'تم قبول الطلب بنجاح',
          style: TextStyle(fontFamily: 'Tajawal'),
        ),
        backgroundColor: Color(0xFF00C853),
        behavior: SnackBarBehavior.floating,
        duration: Duration(seconds: 2),
      ),
    );
  }



  _StatusInfo _getStatusInfo(String status) {
    switch (status) {
      case 'new':
        return _StatusInfo(Colors.blue, Icons.new_releases, 'جديد');
      case 'pending':
        return _StatusInfo(Colors.orange, Icons.timer, 'بانتظار القبول');
      case 'accepted':
        return _StatusInfo(
          const Color(0xFF00C853),
          Icons.check_circle,
          'مقبول',
        );
      case 'ready':
        return _StatusInfo(
          Colors.purple,
          Icons.delivery_dining,
          'جاهز للتسليم',
        );
      default:
        return _StatusInfo(Colors.grey, Icons.info, 'غير معروف');
    }
  }

  Widget _buildBottomNavBar(Color primaryColor, Color textColor) {
    return BottomNavigationBar(
      currentIndex: _currentIndex,
      onTap: (index) {
        setState(() {
          _currentIndex = index;
        });
      },
      backgroundColor: _isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
      selectedItemColor: primaryColor,
      unselectedItemColor: textColor.withValues(alpha: 0.5),
      iconSize: 20,
      selectedFontSize: 10,
      unselectedFontSize: 9,
      selectedLabelStyle: const TextStyle(
        fontFamily: 'Tajawal',
        fontSize: 10,
        fontWeight: FontWeight.bold,
      ),
      unselectedLabelStyle: const TextStyle(fontFamily: 'Tajawal', fontSize: 9),
      type: BottomNavigationBarType.fixed,
      items: const [
        BottomNavigationBarItem(icon: Icon(Icons.assignment), label: 'الطلبات'),
        BottomNavigationBarItem(icon: Icon(Icons.assignment_turned_in), label: 'المقبولة'),
        BottomNavigationBarItem(icon: Icon(Icons.inventory), label: 'المخزون'),
        BottomNavigationBarItem(icon: Icon(Icons.analytics), label: 'التقارير'),
        BottomNavigationBarItem(icon: Icon(Icons.settings), label: 'الإعدادات'),
      ],
    );
  }
}

class _StatusInfo {
  final Color color;
  final IconData icon;
  final String text;

  _StatusInfo(this.color, this.icon, this.text);
}
